# CDSS Demo - 截图水印工具

这是一个使用Swift和SwiftUI开发的iOS截图水印应用，可以为截图或选择的图片添加各种样式的水印。

## 功能特点

### 🎯 核心功能
- **自动截屏**：一键截取当前屏幕内容
- **水印添加**：支持文字水印和Logo水印
- **多种样式**：简单文字、带背景、带图标、自定义样式
- **灵活定位**：支持5个位置（左上、右上、左下、右下、居中）
- **自动保存**：处理后的图片自动保存到相册

### 🎨 水印样式
1. **简单文字**：纯文字水印，简洁明了
2. **带背景**：文字带半透明黑色背景，更加醒目
3. **带图标**：文字前添加星形图标，更有设计感
4. **自定义**：带阴影效果的粗体文字，个性化十足

### ⚙️ 自定义选项
- 水印文字内容
- 字体大小调节（12-32pt）
- 透明度控制（10%-100%）
- 颜色自定义
- 位置选择
- 样式切换

## 项目结构

```
cdss-demo/
├── cdss_demoApp.swift          # 应用入口
├── ContentView.swift           # 主界面
├── WatermarkManager.swift      # 水印管理器（核心功能）
├── WatermarkCustomizationView.swift  # 自定义设置界面
├── WatermarkExampleView.swift  # 功能演示界面
├── Info.plist                  # 应用配置（包含相册权限）
└── Assets.xcassets            # 资源文件
```

## 核心代码说明

### 1. WatermarkManager.swift
这是核心的水印管理类，包含以下主要功能：

```swift
class WatermarkManager: ObservableObject {
    // 截屏并添加水印
    func takeScreenshotWithWatermark(watermarkText: String, logoName: String?)
    
    // 给图片添加水印
    func addWatermarkToImage(_ originalImage: UIImage, watermarkText: String, logoName: String?) -> UIImage
    
    // 自定义文字水印
    func addTextWatermark(_ text: String, to imageSize: CGSize, position: WatermarkPosition, style: WatermarkStyle, fontSize: CGFloat, opacity: CGFloat, color: UIColor)
    
    // 保存到相册
    func saveImageToPhotoLibrary(_ image: UIImage)
}
```

### 2. 水印实现原理

使用`UIGraphicsImageRenderer`进行图像渲染：

```swift
let renderer = UIGraphicsImageRenderer(size: originalImage.size)
return renderer.image { context in
    // 1. 绘制原始图片
    originalImage.draw(in: CGRect(origin: .zero, size: originalImage.size))
    
    // 2. 添加Logo水印
    if let logo = logoImage {
        addLogoWatermark(logo, to: originalImage.size)
    }
    
    // 3. 添加文字水印
    addTextWatermark(watermarkText, to: originalImage.size)
}
```

### 3. 权限配置

在`Info.plist`中添加相册访问权限：

```xml
<key>NSPhotoLibraryAddUsageDescription</key>
<string>需要访问相册来保存带水印的截图</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>需要访问相册功能，用于保存截图</string>
```

## 使用方法

### 基本使用
1. 打开应用
2. 在主界面输入水印文字
3. 点击"截图并添加水印"按钮
4. 应用会自动截屏、添加水印并保存到相册

### 高级自定义
1. 点击"自定义设置"按钮
2. 调整字体大小、透明度、颜色等参数
3. 选择水印位置和样式
4. 在预览区域查看效果
5. 点击"完成"保存设置

### 图片水印演示
1. 点击"图片水印演示"按钮
2. 选择相册中的图片
3. 尝试不同的水印样式
4. 保存处理后的图片

## 技术要点

### 1. 现代iOS开发最佳实践
- 使用`UIGraphicsImageRenderer`而不是过时的`UIGraphicsBeginImageContext`
- 支持广色域和高分辨率显示
- 遵循iOS内存管理最佳实践

### 2. SwiftUI界面设计
- 响应式布局设计
- 状态管理和数据绑定
- 自定义组件和样式

### 3. 权限处理
- 相册访问权限请求
- 用户友好的错误提示
- 异步操作处理

### 4. 图像处理优化
- 高效的图像渲染
- 内存使用优化
- 支持不同屏幕尺寸

## 扩展功能建议

1. **更多水印样式**：渐变文字、描边效果、阴影样式
2. **批量处理**：支持多张图片批量添加水印
3. **模板系统**：预设水印模板，快速应用
4. **云端同步**：水印设置云端保存和同步
5. **分享功能**：直接分享到社交媒体

## 系统要求

- iOS 14.0+
- Xcode 13.0+
- Swift 5.5+

## 许可证

MIT License - 详见LICENSE文件

---

**开发者**: kit.yeung  
**创建日期**: 2025/7/31  
**版本**: 1.0.0
