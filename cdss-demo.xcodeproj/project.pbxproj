// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		2A6C96622DB5F5DA00D161DE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A6C964B2DB5F5D900D161DE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2A6C96522DB5F5D900D161DE;
			remoteInfo = "cdss-demo";
		};
		2A6C966C2DB5F5DA00D161DE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A6C964B2DB5F5D900D161DE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2A6C96522DB5F5D900D161DE;
			remoteInfo = "cdss-demo";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2A6C96532DB5F5D900D161DE /* cdss-demo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "cdss-demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		2A6C96612DB5F5DA00D161DE /* cdss-demoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "cdss-demoTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		2A6C966B2DB5F5DA00D161DE /* cdss-demoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "cdss-demoUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		2AF5F4DB2E3B240400933AE0 /* Exceptions for "cdss-demo" folder in "cdss-demo" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 2A6C96522DB5F5D900D161DE /* cdss-demo */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		2A6C96552DB5F5D900D161DE /* cdss-demo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				2AF5F4DB2E3B240400933AE0 /* Exceptions for "cdss-demo" folder in "cdss-demo" target */,
			);
			path = "cdss-demo";
			sourceTree = "<group>";
		};
		2A6C96642DB5F5DA00D161DE /* cdss-demoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "cdss-demoTests";
			sourceTree = "<group>";
		};
		2A6C966E2DB5F5DA00D161DE /* cdss-demoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "cdss-demoUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		2A6C96502DB5F5D900D161DE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A6C965E2DB5F5DA00D161DE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A6C96682DB5F5DA00D161DE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2A6C964A2DB5F5D900D161DE = {
			isa = PBXGroup;
			children = (
				2A6C96552DB5F5D900D161DE /* cdss-demo */,
				2A6C96642DB5F5DA00D161DE /* cdss-demoTests */,
				2A6C966E2DB5F5DA00D161DE /* cdss-demoUITests */,
				2A6C96542DB5F5D900D161DE /* Products */,
			);
			sourceTree = "<group>";
		};
		2A6C96542DB5F5D900D161DE /* Products */ = {
			isa = PBXGroup;
			children = (
				2A6C96532DB5F5D900D161DE /* cdss-demo.app */,
				2A6C96612DB5F5DA00D161DE /* cdss-demoTests.xctest */,
				2A6C966B2DB5F5DA00D161DE /* cdss-demoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2A6C96522DB5F5D900D161DE /* cdss-demo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A6C96752DB5F5DA00D161DE /* Build configuration list for PBXNativeTarget "cdss-demo" */;
			buildPhases = (
				2A6C964F2DB5F5D900D161DE /* Sources */,
				2A6C96502DB5F5D900D161DE /* Frameworks */,
				2A6C96512DB5F5D900D161DE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				2A6C96552DB5F5D900D161DE /* cdss-demo */,
			);
			name = "cdss-demo";
			packageProductDependencies = (
			);
			productName = "cdss-demo";
			productReference = 2A6C96532DB5F5D900D161DE /* cdss-demo.app */;
			productType = "com.apple.product-type.application";
		};
		2A6C96602DB5F5DA00D161DE /* cdss-demoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A6C96782DB5F5DA00D161DE /* Build configuration list for PBXNativeTarget "cdss-demoTests" */;
			buildPhases = (
				2A6C965D2DB5F5DA00D161DE /* Sources */,
				2A6C965E2DB5F5DA00D161DE /* Frameworks */,
				2A6C965F2DB5F5DA00D161DE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2A6C96632DB5F5DA00D161DE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2A6C96642DB5F5DA00D161DE /* cdss-demoTests */,
			);
			name = "cdss-demoTests";
			packageProductDependencies = (
			);
			productName = "cdss-demoTests";
			productReference = 2A6C96612DB5F5DA00D161DE /* cdss-demoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2A6C966A2DB5F5DA00D161DE /* cdss-demoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2A6C967B2DB5F5DA00D161DE /* Build configuration list for PBXNativeTarget "cdss-demoUITests" */;
			buildPhases = (
				2A6C96672DB5F5DA00D161DE /* Sources */,
				2A6C96682DB5F5DA00D161DE /* Frameworks */,
				2A6C96692DB5F5DA00D161DE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				2A6C966D2DB5F5DA00D161DE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				2A6C966E2DB5F5DA00D161DE /* cdss-demoUITests */,
			);
			name = "cdss-demoUITests";
			packageProductDependencies = (
			);
			productName = "cdss-demoUITests";
			productReference = 2A6C966B2DB5F5DA00D161DE /* cdss-demoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2A6C964B2DB5F5D900D161DE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					2A6C96522DB5F5D900D161DE = {
						CreatedOnToolsVersion = 16.3;
					};
					2A6C96602DB5F5DA00D161DE = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 2A6C96522DB5F5D900D161DE;
					};
					2A6C966A2DB5F5DA00D161DE = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 2A6C96522DB5F5D900D161DE;
					};
				};
			};
			buildConfigurationList = 2A6C964E2DB5F5D900D161DE /* Build configuration list for PBXProject "cdss-demo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2A6C964A2DB5F5D900D161DE;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2A6C96542DB5F5D900D161DE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2A6C96522DB5F5D900D161DE /* cdss-demo */,
				2A6C96602DB5F5DA00D161DE /* cdss-demoTests */,
				2A6C966A2DB5F5DA00D161DE /* cdss-demoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2A6C96512DB5F5D900D161DE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A6C965F2DB5F5DA00D161DE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A6C96692DB5F5DA00D161DE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2A6C964F2DB5F5D900D161DE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A6C965D2DB5F5DA00D161DE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2A6C96672DB5F5DA00D161DE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2A6C96632DB5F5DA00D161DE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2A6C96522DB5F5D900D161DE /* cdss-demo */;
			targetProxy = 2A6C96622DB5F5DA00D161DE /* PBXContainerItemProxy */;
		};
		2A6C966D2DB5F5DA00D161DE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2A6C96522DB5F5D900D161DE /* cdss-demo */;
			targetProxy = 2A6C966C2DB5F5DA00D161DE /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2A6C96732DB5F5DA00D161DE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 969SU2QN85;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2A6C96742DB5F5DA00D161DE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 969SU2QN85;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		2A6C96762DB5F5DA00D161DE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "cdss-demo/cdss_demo.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 969SU2QN85;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "cdss-demo/Info.plist";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册来保存截图";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册功能，用于保存截图";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ctint.cdss-demo";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		2A6C96772DB5F5DA00D161DE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = "cdss-demo/cdss_demo.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 969SU2QN85;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "cdss-demo/Info.plist";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册来保存截图";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册功能，用于保存截图";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ctint.cdss-demo";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		2A6C96792DB5F5DA00D161DE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 969SU2QN85;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ctint.cdss-demoTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/cdss-demo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/cdss-demo";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		2A6C967A2DB5F5DA00D161DE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 969SU2QN85;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ctint.cdss-demoTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/cdss-demo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/cdss-demo";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		2A6C967C2DB5F5DA00D161DE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 969SU2QN85;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ctint.cdss-demoUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "cdss-demo";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		2A6C967D2DB5F5DA00D161DE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 969SU2QN85;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.ctint.cdss-demoUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = "cdss-demo";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2A6C964E2DB5F5D900D161DE /* Build configuration list for PBXProject "cdss-demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A6C96732DB5F5DA00D161DE /* Debug */,
				2A6C96742DB5F5DA00D161DE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A6C96752DB5F5DA00D161DE /* Build configuration list for PBXNativeTarget "cdss-demo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A6C96762DB5F5DA00D161DE /* Debug */,
				2A6C96772DB5F5DA00D161DE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A6C96782DB5F5DA00D161DE /* Build configuration list for PBXNativeTarget "cdss-demoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A6C96792DB5F5DA00D161DE /* Debug */,
				2A6C967A2DB5F5DA00D161DE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2A6C967B2DB5F5DA00D161DE /* Build configuration list for PBXNativeTarget "cdss-demoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2A6C967C2DB5F5DA00D161DE /* Debug */,
				2A6C967D2DB5F5DA00D161DE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2A6C964B2DB5F5D900D161DE /* Project object */;
}
