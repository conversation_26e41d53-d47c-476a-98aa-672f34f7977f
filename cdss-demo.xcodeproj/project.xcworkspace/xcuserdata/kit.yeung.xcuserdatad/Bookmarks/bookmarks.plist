<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>top-level-items</key>
	<array>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>52</string>
					<key>EndingLineNumber</key>
					<string>168</string>
					<key>StartingColumnNumber</key>
					<string>52</string>
					<key>StartingLineNumber</key>
					<string>168</string>
					<key>Timestamp</key>
					<string>775628912.522621</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>cdss-demo/SimpleWatermarkTest.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string></string>
				<key>leading</key>
				<string>    
    private func saveImageToPhotoLibrary(_ image: UIImage) {
        PHPhotoLibrary.requestAuthorization { [weak </string>
				<key>trailing</key>
				<string>self] status in
            DispatchQueue.main.async {
                self?.isProcessing = false
</string>
			</dict>
			<key>title</key>
			<string>Error: 'weak' may only be applied to class and class-bound protocol types, not 'SimpleWatermarkTest'</string>
			<key>type</key>
			<string>bookmark</string>
		</dict>
	</array>
</dict>
</plist>
