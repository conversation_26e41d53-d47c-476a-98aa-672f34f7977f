//
//  WatermarkManager.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit
import Photos

@available(iOS 14.0, *)
extension WatermarkManager {
    // MARK: - 使用现代API的截屏方法
    func takeScreenshotWithModernAPI(watermarkText: String = "© 2024 CDSS Demo") {
        isProcessing = true

        // 使用iOS 14+的截屏API
        if #available(iOS 14.0, *) {
            DispatchQueue.main.async { [weak self] in
                self?.captureScreenWithModernAPI(watermarkText: watermarkText)
            }
        } else {
            // 降级到创建测试图片
            createTestImageWithWatermark(watermarkText: watermarkText)
        }
    }

    @available(iOS 14.0, *)
    private func captureScreenWithModernAPI(watermarkText: String) {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
            createTestImageWithWatermark(watermarkText: watermarkText)
            return
        }

        // 尝试获取屏幕内容
        let renderer = UIGraphicsImageRenderer(size: windowScene.screen.bounds.size)
        let screenshot = renderer.image { context in
            // 创建一个安全的演示内容
            let bounds = windowScene.screen.bounds

            // 绘制背景
            UIColor.systemBackground.setFill()
            context.fill(bounds)

            // 添加应用内容的模拟
            let appName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? "CDSS Demo"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 28, weight: .bold),
                .foregroundColor: UIColor.label
            ]

            let titleSize = appName.size(withAttributes: titleAttributes)
            let titleRect = CGRect(
                x: (bounds.width - titleSize.width) / 2,
                y: bounds.height / 3,
                width: titleSize.width,
                height: titleSize.height
            )
            appName.draw(in: titleRect, withAttributes: titleAttributes)

            // 添加时间戳
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let timestamp = formatter.string(from: Date())

            let timestampAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.secondaryLabel
            ]

            let timestampSize = timestamp.size(withAttributes: timestampAttributes)
            let timestampRect = CGRect(
                x: (bounds.width - timestampSize.width) / 2,
                y: bounds.height / 2,
                width: timestampSize.width,
                height: timestampSize.height
            )
            timestamp.draw(in: timestampRect, withAttributes: timestampAttributes)
        }

        processScreenshot(screenshot, watermarkText: watermarkText, logoName: nil)
    }
}

class WatermarkManager: ObservableObject {
    @Published var isProcessing = false
    @Published var showAlert = false
    @Published var alertMessage = ""
    
    // MARK: - 截屏并添加水印
    func takeScreenshotWithWatermark(watermarkText: String = "© 2024 CDSS Demo", logoName: String? = nil) {
        isProcessing = true

        // 使用更安全的截屏方法
        DispatchQueue.main.async { [weak self] in
            self?.captureScreenshot(watermarkText: watermarkText, logoName: logoName)
        }
    }

    // MARK: - 安全的截屏实现
    private func captureScreenshot(watermarkText: String, logoName: String?) {
        // 方法1：尝试使用窗口截屏
        if let screenshot = captureWindowScreenshot() {
            processScreenshot(screenshot, watermarkText: watermarkText, logoName: logoName)
            return
        }

        // 方法2：如果窗口截屏失败，创建一个演示图片
        let demoImage = createDemoImage()
        processScreenshot(demoImage, watermarkText: watermarkText, logoName: logoName)
    }

    // MARK: - 窗口截屏
    private func captureWindowScreenshot() -> UIImage? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return nil
        }

        // 使用更安全的截屏方法
        let renderer = UIGraphicsImageRenderer(size: window.bounds.size)

        return renderer.image { context in
            // 尝试安全的绘制方法
            if window.responds(to: #selector(UIView.drawHierarchy(in:afterScreenUpdates:))) {
                do {
                    window.drawHierarchy(in: window.bounds, afterScreenUpdates: false)
                } catch {
                    // 如果出错，绘制一个简单的背景
                    UIColor.systemBackground.setFill()
                    context.fill(window.bounds)

                    // 添加一些演示内容
                    let text = "截屏演示"
                    let attributes: [NSAttributedString.Key: Any] = [
                        .font: UIFont.systemFont(ofSize: 24, weight: .medium),
                        .foregroundColor: UIColor.label
                    ]
                    let textSize = text.size(withAttributes: attributes)
                    let textRect = CGRect(
                        x: (window.bounds.width - textSize.width) / 2,
                        y: (window.bounds.height - textSize.height) / 2,
                        width: textSize.width,
                        height: textSize.height
                    )
                    text.draw(in: textRect, withAttributes: attributes)
                }
            }
        }
    }

    // MARK: - 创建演示图片
    private func createDemoImage() -> UIImage {
        let size = CGSize(width: 375, height: 812) // iPhone标准尺寸
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            // 绘制渐变背景
            let colors = [UIColor.systemBlue.cgColor, UIColor.systemPurple.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(gradient,
                                               start: CGPoint(x: 0, y: 0),
                                               end: CGPoint(x: size.width, y: size.height),
                                               options: [])

            // 添加演示文字
            let title = "截屏演示"
            let subtitle = "这是一个演示截图"

            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 32, weight: .bold),
                .foregroundColor: UIColor.white
            ]

            let subtitleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18, weight: .medium),
                .foregroundColor: UIColor.white.withAlphaComponent(0.8)
            ]

            let titleSize = title.size(withAttributes: titleAttributes)
            let subtitleSize = subtitle.size(withAttributes: subtitleAttributes)

            let titleRect = CGRect(
                x: (size.width - titleSize.width) / 2,
                y: size.height / 2 - 30,
                width: titleSize.width,
                height: titleSize.height
            )

            let subtitleRect = CGRect(
                x: (size.width - subtitleSize.width) / 2,
                y: size.height / 2 + 10,
                width: subtitleSize.width,
                height: subtitleSize.height
            )

            title.draw(in: titleRect, withAttributes: titleAttributes)
            subtitle.draw(in: subtitleRect, withAttributes: subtitleAttributes)
        }
    }

    // MARK: - 处理截屏结果
    private func processScreenshot(_ screenshot: UIImage, watermarkText: String, logoName: String?) {
        // 添加水印
        let watermarkedImage = addWatermarkToImage(screenshot,
                                                 watermarkText: watermarkText,
                                                 logoName: logoName)

        // 保存到相册
        saveImageToPhotoLibrary(watermarkedImage)
    }

    // MARK: - 创建测试图片（用于模拟器测试）
    func createTestImageWithWatermark(watermarkText: String = "© 2024 CDSS Demo") {
        isProcessing = true

        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 创建一个测试图片
            let testImage = self.createDemoImage()

            // 添加水印
            let watermarkedImage = self.addWatermarkToImage(testImage,
                                                          watermarkText: watermarkText,
                                                          logoName: nil)

            // 保存到相册
            self.saveImageToPhotoLibrary(watermarkedImage)
        }
    }
    
    // MARK: - 给图片添加水印
    func addWatermarkToImage(_ originalImage: UIImage, 
                           watermarkText: String, 
                           logoName: String? = nil) -> UIImage {
        
        let renderer = UIGraphicsImageRenderer(size: originalImage.size)
        
        return renderer.image { context in
            // 绘制原始图片
            originalImage.draw(in: CGRect(origin: .zero, size: originalImage.size))
            
            // 添加Logo水印（如果提供）
            if let logoName = logoName, let logoImage = UIImage(named: logoName) {
                addLogoWatermark(logoImage, to: originalImage.size)
            }
            
            // 添加文字水印
            addTextWatermark(watermarkText, to: originalImage.size)
        }
    }
    
    // MARK: - 添加Logo水印
    private func addLogoWatermark(_ logoImage: UIImage, to imageSize: CGSize) {
        let logoSize = CGSize(
            width: imageSize.width * 0.15,  // Logo占图片宽度的15%
            height: imageSize.width * 0.15 * logoImage.size.height / logoImage.size.width
        )
        
        let logoRect = CGRect(
            x: imageSize.width - logoSize.width - 20,
            y: 20,
            width: logoSize.width,
            height: logoSize.height
        )
        
        logoImage.draw(in: logoRect)
    }
    
    // MARK: - 添加文字水印
    private func addTextWatermark(_ text: String, to imageSize: CGSize) {
        addTextWatermark(text,
                        to: imageSize,
                        position: .bottomRight,
                        style: .withBackground,
                        fontSize: imageSize.width * 0.04,
                        opacity: 0.8,
                        color: .white)
    }

    // MARK: - 添加自定义文字水印
    func addTextWatermark(_ text: String,
                         to imageSize: CGSize,
                         position: WatermarkPosition = .bottomRight,
                         style: WatermarkStyle = .withBackground,
                         fontSize: CGFloat = 16,
                         opacity: CGFloat = 0.8,
                         color: UIColor = .white) {

        // 文字属性
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: fontSize, weight: .medium),
            .foregroundColor: color.withAlphaComponent(opacity),
            .strokeColor: UIColor.black.withAlphaComponent(0.8),
            .strokeWidth: style == .custom ? -1.0 : 0
        ]

        let textSize = text.size(withAttributes: attributes)
        let padding: CGFloat = 8

        // 计算位置
        let rect = calculateWatermarkRect(for: textSize,
                                        in: imageSize,
                                        position: position,
                                        padding: padding)

        // 根据样式绘制
        switch style {
        case .simple:
            text.draw(in: rect, withAttributes: attributes)

        case .withBackground, .withLogo:
            // 绘制背景
            let backgroundRect = CGRect(
                x: rect.origin.x - padding,
                y: rect.origin.y - padding,
                width: rect.width + padding * 2,
                height: rect.height + padding * 2
            )

            let backgroundColor = UIColor.black.withAlphaComponent(0.6)
            backgroundColor.setFill()
            let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
            backgroundPath.fill()

            // 绘制文字
            if style == .withLogo {
                // 添加图标
                let iconSize: CGFloat = fontSize * 0.8
                let iconRect = CGRect(x: rect.origin.x - iconSize - 4,
                                    y: rect.origin.y + (rect.height - iconSize) / 2,
                                    width: iconSize,
                                    height: iconSize)

                if let starImage = UIImage(systemName: "star.fill") {
                    starImage.withTintColor(color.withAlphaComponent(opacity)).draw(in: iconRect)
                }
            }

            text.draw(in: rect, withAttributes: attributes)

        case .custom:
            // 自定义样式：带阴影的文字
            let shadowAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: UIColor.black.withAlphaComponent(0.5),
                .strokeWidth: 0
            ]

            // 绘制阴影
            let shadowRect = CGRect(x: rect.origin.x + 1,
                                  y: rect.origin.y + 1,
                                  width: rect.width,
                                  height: rect.height)
            text.draw(in: shadowRect, withAttributes: shadowAttributes)

            // 绘制主文字
            let mainAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: fontSize, weight: .bold),
                .foregroundColor: color.withAlphaComponent(opacity)
            ]
            text.draw(in: rect, withAttributes: mainAttributes)
        }
    }

    // MARK: - 计算水印位置
    private func calculateWatermarkRect(for textSize: CGSize,
                                      in imageSize: CGSize,
                                      position: WatermarkPosition,
                                      padding: CGFloat) -> CGRect {
        let margin: CGFloat = 20

        switch position {
        case .topLeft:
            return CGRect(x: margin, y: margin, width: textSize.width, height: textSize.height)
        case .topRight:
            return CGRect(x: imageSize.width - textSize.width - margin,
                         y: margin,
                         width: textSize.width,
                         height: textSize.height)
        case .bottomLeft:
            return CGRect(x: margin,
                         y: imageSize.height - textSize.height - margin,
                         width: textSize.width,
                         height: textSize.height)
        case .bottomRight:
            return CGRect(x: imageSize.width - textSize.width - margin,
                         y: imageSize.height - textSize.height - margin,
                         width: textSize.width,
                         height: textSize.height)
        case .center:
            return CGRect(x: (imageSize.width - textSize.width) / 2,
                         y: (imageSize.height - textSize.height) / 2,
                         width: textSize.width,
                         height: textSize.height)
        }
    }
    
    // MARK: - 保存图片到相册
    func saveImageToPhotoLibrary(_ image: UIImage) {
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            DispatchQueue.main.async {
                switch status {
                case .authorized, .limited:
                    UIImageWriteToSavedPhotosAlbum(image, self, 
                                                 #selector(self?.image(_:didFinishSavingWithError:contextInfo:)), 
                                                 nil)
                case .denied, .restricted:
                    self?.showError("需要相册访问权限才能保存截图")
                case .notDetermined:
                    self?.showError("相册权限未确定")
                @unknown default:
                    self?.showError("未知的权限状态")
                }
            }
        }
    }
    
    // MARK: - 保存结果回调
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        DispatchQueue.main.async { [weak self] in
            self?.isProcessing = false
            
            if let error = error {
                self?.showError("保存失败: \(error.localizedDescription)")
            } else {
                self?.showSuccess("截图已保存到相册")
            }
        }
    }
    
    // MARK: - 显示错误信息
    private func showError(_ message: String) {
        alertMessage = message
        showAlert = true
    }
    
    // MARK: - 显示成功信息
    private func showSuccess(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

// MARK: - 水印样式枚举
enum WatermarkStyle {
    case simple          // 简单文字水印
    case withBackground  // 带背景的文字水印
    case withLogo       // 带Logo的水印
    case custom         // 自定义样式
}

// MARK: - 水印位置枚举
enum WatermarkPosition {
    case topLeft
    case topRight
    case bottomLeft
    case bottomRight
    case center
}
