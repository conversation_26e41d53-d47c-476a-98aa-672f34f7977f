//
//  WatermarkExampleView.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI
import UIKit

struct WatermarkExampleView: View {
    @StateObject private var watermarkManager = WatermarkManager()
    @State private var selectedImage: UIImage?
    @State private var showImagePicker = false
    @State private var processedImage: UIImage?
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 标题
                Text("水印功能演示")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 图片选择区域
                VStack(spacing: 15) {
                    if let image = selectedImage {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 200)
                            .cornerRadius(10)
                            .shadow(radius: 5)
                    } else {
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(.systemGray5))
                            .frame(height: 200)
                            .overlay(
                                VStack {
                                    Image(systemName: "photo")
                                        .font(.system(size: 40))
                                        .foregroundColor(.gray)
                                    Text("选择图片")
                                        .foregroundColor(.gray)
                                }
                            )
                    }
                    
                    Button("选择图片") {
                        showImagePicker = true
                    }
                    .buttonStyle(.bordered)
                }
                
                // 水印样式演示
                if selectedImage != nil {
                    VStack(spacing: 15) {
                        Text("水印样式预览")
                            .font(.headline)
                        
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 15) {
                            WatermarkStyleButton(title: "简单文字", style: .simple) {
                                applyWatermark(style: .simple)
                            }
                            
                            WatermarkStyleButton(title: "带背景", style: .withBackground) {
                                applyWatermark(style: .withBackground)
                            }
                            
                            WatermarkStyleButton(title: "带图标", style: .withLogo) {
                                applyWatermark(style: .withLogo)
                            }
                            
                            WatermarkStyleButton(title: "自定义", style: .custom) {
                                applyWatermark(style: .custom)
                            }
                        }
                    }
                }
                
                // 处理后的图片
                if let processedImage = processedImage {
                    VStack(spacing: 10) {
                        Text("添加水印后")
                            .font(.headline)
                        
                        Image(uiImage: processedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxHeight: 300)
                            .cornerRadius(10)
                            .shadow(radius: 5)
                        
                        Button("保存到相册") {
                            watermarkManager.saveImageToPhotoLibrary(processedImage)
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                
                // 使用说明
                VStack(alignment: .leading, spacing: 10) {
                    Text("使用说明")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 5) {
                        HStack {
                            Image(systemName: "1.circle.fill")
                                .foregroundColor(.blue)
                            Text("选择一张图片或使用截图功能")
                        }
                        
                        HStack {
                            Image(systemName: "2.circle.fill")
                                .foregroundColor(.blue)
                            Text("选择喜欢的水印样式")
                        }
                        
                        HStack {
                            Image(systemName: "3.circle.fill")
                                .foregroundColor(.blue)
                            Text("保存带水印的图片到相册")
                        }
                    }
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(10)
                
                Spacer(minLength: 50)
            }
            .padding()
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .alert("提示", isPresented: $watermarkManager.showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(watermarkManager.alertMessage)
        }
    }
    
    private func applyWatermark(style: WatermarkStyle) {
        guard let image = selectedImage else { return }
        
        let renderer = UIGraphicsImageRenderer(size: image.size)
        processedImage = renderer.image { context in
            // 绘制原始图片
            image.draw(in: CGRect(origin: .zero, size: image.size))
            
            // 添加水印
            watermarkManager.addTextWatermark("© 2024 CDSS Demo",
                                            to: image.size,
                                            position: .bottomRight,
                                            style: style,
                                            fontSize: image.size.width * 0.04,
                                            opacity: 0.8,
                                            color: .white)
        }
    }
}

// MARK: - 水印样式按钮
struct WatermarkStyleButton: View {
    let title: String
    let style: WatermarkStyle
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                styleIcon
                Text(title)
                    .font(.caption)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(10)
        }
        .foregroundColor(.primary)
    }
    
    @ViewBuilder
    private var styleIcon: some View {
        switch style {
        case .simple:
            Image(systemName: "textformat")
                .font(.title2)
        case .withBackground:
            Image(systemName: "rectangle.fill.on.rectangle.fill")
                .font(.title2)
        case .withLogo:
            Image(systemName: "star.fill")
                .font(.title2)
        case .custom:
            Image(systemName: "paintbrush.fill")
                .font(.title2)
        }
    }
}

// MARK: - 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

#Preview {
    WatermarkExampleView()
}
