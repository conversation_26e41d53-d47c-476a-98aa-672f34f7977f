//
//  WatermarkCustomizationView.swift
//  cdss-demo
//
//  Created by kit.yeung on 2025/7/31.
//

import SwiftUI

struct WatermarkCustomizationView: View {
    @Binding var watermarkText: String
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedPosition: WatermarkPosition = .bottomRight
    @State private var selectedStyle: WatermarkStyle = .withBackground
    @State private var fontSize: Double = 16
    @State private var opacity: Double = 0.8
    @State private var useCustomColor = false
    @State private var watermarkColor = Color.white
    
    var body: some View {
        NavigationView {
            Form {
                // 水印文字设置
                Section("水印文字") {
                    TextField("水印文字", text: $watermarkText)
                    
                    HStack {
                        Text("字体大小")
                        Spacer()
                        Text("\(Int(fontSize))")
                            .foregroundColor(.secondary)
                    }
                    
                    Slider(value: $fontSize, in: 12...32, step: 1) {
                        Text("字体大小")
                    }
                }
                
                // 水印样式设置
                Section("水印样式") {
                    Picker("样式", selection: $selectedStyle) {
                        Text("简单文字").tag(WatermarkStyle.simple)
                        Text("带背景").tag(WatermarkStyle.withBackground)
                        Text("带Logo").tag(WatermarkStyle.withLogo)
                        Text("自定义").tag(WatermarkStyle.custom)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                // 水印位置设置
                Section("水印位置") {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 10) {
                        PositionButton(position: .topLeft, selectedPosition: $selectedPosition, title: "左上")
                        PositionButton(position: .center, selectedPosition: $selectedPosition, title: "居中")
                        PositionButton(position: .topRight, selectedPosition: $selectedPosition, title: "右上")
                        
                        Spacer()
                            .gridCellUnsizedAxes(.horizontal)
                        Spacer()
                            .gridCellUnsizedAxes(.horizontal)
                        Spacer()
                            .gridCellUnsizedAxes(.horizontal)
                        
                        PositionButton(position: .bottomLeft, selectedPosition: $selectedPosition, title: "左下")
                        Spacer()
                            .gridCellUnsizedAxes(.horizontal)
                        PositionButton(position: .bottomRight, selectedPosition: $selectedPosition, title: "右下")
                    }
                    .padding(.vertical, 8)
                }
                
                // 高级设置
                Section("高级设置") {
                    HStack {
                        Text("透明度")
                        Spacer()
                        Text("\(Int(opacity * 100))%")
                            .foregroundColor(.secondary)
                    }
                    
                    Slider(value: $opacity, in: 0.1...1.0, step: 0.1) {
                        Text("透明度")
                    }
                    
                    Toggle("自定义颜色", isOn: $useCustomColor)
                    
                    if useCustomColor {
                        ColorPicker("水印颜色", selection: $watermarkColor)
                    }
                }
                
                // 预览区域
                Section("预览效果") {
                    WatermarkPreviewView(
                        text: watermarkText,
                        position: selectedPosition,
                        style: selectedStyle,
                        fontSize: fontSize,
                        opacity: opacity,
                        color: useCustomColor ? watermarkColor : .white
                    )
                    .frame(height: 200)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
            }
            .navigationTitle("自定义水印")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// MARK: - 位置选择按钮
struct PositionButton: View {
    let position: WatermarkPosition
    @Binding var selectedPosition: WatermarkPosition
    let title: String
    
    var body: some View {
        Button(action: {
            selectedPosition = position
        }) {
            Text(title)
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(selectedPosition == position ? Color.blue : Color(.systemGray5))
                .foregroundColor(selectedPosition == position ? .white : .primary)
                .cornerRadius(8)
        }
    }
}

// MARK: - 水印预览视图
struct WatermarkPreviewView: View {
    let text: String
    let position: WatermarkPosition
    let style: WatermarkStyle
    let fontSize: Double
    let opacity: Double
    let color: Color
    
    var body: some View {
        ZStack {
            // 模拟的背景内容
            VStack {
                HStack {
                    Image(systemName: "photo")
                        .font(.largeTitle)
                        .foregroundColor(.gray)
                    Spacer()
                }
                Spacer()
                HStack {
                    Spacer()
                    Text("预览区域")
                        .font(.title2)
                        .foregroundColor(.gray)
                    Spacer()
                }
                Spacer()
            }
            .padding()
            
            // 水印预览
            watermarkView
        }
    }
    
    @ViewBuilder
    private var watermarkView: some View {
        VStack {
            if position == .topLeft || position == .topRight || position == .center {
                HStack {
                    if position == .topLeft || position == .center {
                        watermarkContent
                    }
                    if position != .center {
                        Spacer()
                    }
                    if position == .topRight {
                        watermarkContent
                    }
                }
                .padding(.top, position == .center ? 0 : 8)
            }
            
            if position == .center {
                Spacer()
            }
            
            if position == .bottomLeft || position == .bottomRight {
                Spacer()
                HStack {
                    if position == .bottomLeft {
                        watermarkContent
                    }
                    Spacer()
                    if position == .bottomRight {
                        watermarkContent
                    }
                }
                .padding(.bottom, 8)
            }
        }
        .padding(.horizontal, 8)
    }
    
    @ViewBuilder
    private var watermarkContent: some View {
        Group {
            switch style {
            case .simple:
                Text(text)
                    .font(.system(size: fontSize))
                    .foregroundColor(color.opacity(opacity))
                    
            case .withBackground:
                Text(text)
                    .font(.system(size: fontSize))
                    .foregroundColor(color)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(6)
                    .opacity(opacity)
                    
            case .withLogo:
                HStack(spacing: 4) {
                    Image(systemName: "star.fill")
                        .font(.system(size: fontSize * 0.8))
                    Text(text)
                        .font(.system(size: fontSize))
                }
                .foregroundColor(color)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.black.opacity(0.6))
                .cornerRadius(6)
                .opacity(opacity)
                
            case .custom:
                Text(text)
                    .font(.system(size: fontSize, weight: .bold))
                    .foregroundColor(color)
                    .shadow(color: .black, radius: 1, x: 1, y: 1)
                    .opacity(opacity)
            }
        }
    }
}

#Preview {
    WatermarkCustomizationView(watermarkText: .constant("© 2024 CDSS Demo"))
}
